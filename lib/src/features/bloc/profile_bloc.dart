import 'package:rxdart/rxdart.dart';

import '../../core/response/category_response.dart';
import '../../core/response/profile_response.dart';
import '../repository/profile_reposirory.dart';
import '../views/story/widgets/reel_actions/discussion_current.dart';

class ProfileBloc {
  final ProfileRepository _repository = ProfileRepository();

  final BehaviorSubject<ProfileResponse> _subject =
      BehaviorSubject<ProfileResponse>();
  // final BehaviorSubject<CategoryFavoriteResponse> _subject2 =
  //     BehaviorSubject<CategoryFavoriteResponse>();
  final BehaviorSubject<CategoryFavoriteResponse> _subject3 =
      BehaviorSubject<CategoryFavoriteResponse>();

  Future<ProfileResponse> getProfile() async {
    await authController.isloggedin();

    if (authController.isLogged) {
      ProfileResponse response = await _repository.getProfile();

      _subject.sink.add(response);
      return response;
    }

    return ProfileResponse.withError('');
  }

  editProfile(String name, String email, String phone, phoneCode) async {
    int code = 0;
    String message = 'Something went wrong.';
    ProfileResponse response =
        await _repository.editProfile(name, email, phone, phoneCode);

    // ignore: unrelated_type_equality_checks
    if (response.code == "1") {
      code = response.code;
      message = response.msg;

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  getfavouritecategory(int page, int size) async {
    CategoryFavoriteResponse response =
        await _repository.getfavouritecategory(page, size);
    _subject3.sink.add(response);
  }

  // getfavouriteuserVibes(int page, int size) async {
  //   CategoryFavoriteResponse response =
  //       await _repository.getfavouriteuserreels(page, size);
  //   _subject2.sink.add(response);
  // }

  deleteAccount() async {
    await _repository.deleteAccount();
  }

  BehaviorSubject<ProfileResponse> get subject => _subject;
  // BehaviorSubject<CategoryFavoriteResponse> get subject2 => _subject2;
  BehaviorSubject<CategoryFavoriteResponse> get subject3 => _subject3;
  dispose() {
    _subject.close();
    // _subject2.close();
    _subject3.close();
  }
}

final bloc2 = ProfileBloc();
