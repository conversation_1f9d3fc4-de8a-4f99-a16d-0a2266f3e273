import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:page/src/features/controllers/language_controller.dart';

class ProjectPlanModel {
  String? bedroomsEn;
  String? bedroomsAr;
  num? priceFrom;
  num? priceTo;
  String? spaceSizeEn;
  String? spaceSizeAr;

  ProjectPlanModel({
    this.bedroomsEn,
    this.bedroomsAr,
    this.priceFrom,
    this.priceTo,
    this.spaceSizeEn,
    this.spaceSizeAr,
  });

  ProjectPlanModel.fromJson(Map<String, dynamic> json) {
    bedroomsEn = json['bedrooms'] ?? '';
    bedroomsAr = json['bedrooms'] ?? '';
    priceFrom = json['price_from'];
    priceTo = json['price_to'];
    spaceSizeEn = json['space_size_en'] ?? '';
    spaceSizeAr = json['space_size_ar'] ?? '';
  }

  String get bedrooms {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? bedroomsEn ?? bedroomsAr ?? ''
        : bedroomsAr ?? bedroomsEn ?? '';
  }

  String get spaceSize {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? spaceSizeEn ?? ''
        : spaceSizeAr ?? '';
  }
}

class MediaModel {
  int? id;
  String? url;

  MediaModel({this.id, this.url});

  MediaModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    url = json['url'];
  }
}

class FloorPlanModel {
  int? id;
  int? videoId;
  String? name;
  String? nameAr;
  String? nameEn;
  List<MediaModel>? media;

  FloorPlanModel({
    this.id,
    this.videoId,
    this.name,
    this.nameAr,
    this.nameEn,
    this.media,
  });

  FloorPlanModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    name = json['name'];
    nameAr = json['name_ar'];
    nameEn = json['name_en'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }

  String get displayName {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? nameEn ?? name ?? ''
        : nameAr ?? name ?? '';
  }
}

class ReraPermitModel {
  int? id;
  int? videoId;
  String? reraNumber;
  List<MediaModel>? media;

  ReraPermitModel({
    this.id,
    this.videoId,
    this.reraNumber,
    this.media,
  });

  ReraPermitModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    reraNumber = json['rera_number'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }
}

class PropertyStatusModel {
  int? id;
  String? name;
  String? nameAr;
  String? nameEn;

  PropertyStatusModel({
    this.id,
    this.name,
    this.nameAr,
    this.nameEn,
  });

  PropertyStatusModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nameAr = json['name_ar'];
    nameEn = json['name_en'];
  }

  String get displayName {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? nameEn ?? name ?? ''
        : nameAr ?? name ?? '';
  }
}

class PricePlanDataItem extends Equatable {
  final String? dateEn;
  final String? dateAr;
  final String? order;
  final num? payment;
  final String? installmentEn;
  final String? installmentAr;
  final String id; // Unique identifier for each item

  const PricePlanDataItem({
    this.dateEn,
    this.dateAr,
    this.order,
    this.payment,
    this.installmentEn,
    this.installmentAr,
    String? id,
  }) : id = id ?? '';

  factory PricePlanDataItem.fromJson(Map<String?, dynamic> json) {
    log('asfasfasfa ${json['Payment']}');

    return PricePlanDataItem(
      dateEn: json['Date'] is Map
          ? json['Date']['en'] as String?
          : json['Date'] as String?,
      dateAr: json['Date'] is Map ? json['Date']['ar'] as String? : null,
      order: json['order'] as String?,
      payment: num.tryParse(json['Payment'].toString()),
      installmentEn: json['installment'] is Map
          ? json['installment']['en'] as String?
          : json['installment'] as String?,
      installmentAr: json['installment'] is Map
          ? json['installment']['ar'] as String?
          : null,
      id: (json['order'] as String?) ??
          DateTime.now().millisecondsSinceEpoch.toString(),
    );
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['Date'] = {
      'en': dateEn,
      'ar': dateAr,
    };
    data['order'] = order;
    data['Payment'] = payment;
    data['installment'] = {
      'en': installmentEn,
      'ar': installmentAr,
    };
    // Note: ID is not included in JSON as it's only for UI purposes
    return data;
  }

  PricePlanDataItem copyWith({
    String? dateEn,
    String? dateAr,
    String? order,
    num? payment,
    String? installmentEn,
    String? installmentAr,
    String? id,
  }) {
    return PricePlanDataItem(
      dateEn: dateEn ?? this.dateEn,
      dateAr: dateAr ?? this.dateAr,
      order: order ?? this.order,
      payment: payment ?? this.payment,
      installmentEn: installmentEn ?? this.installmentEn,
      installmentAr: installmentAr ?? this.installmentAr,
      id: id ?? this.id,
    );
  }

  @override
  List<Object?> get props =>
      [dateEn, dateAr, order, payment, installmentEn, installmentAr, id];
}

class PricePlanModel extends Equatable {
  final int? id;
  final String? name;
  final String? nameAr;
  final String? nameEn;
  final List<PricePlanDataItem>? data;
  final String? description;
  final String? descriptionAr;
  final String? descriptionEn;

  const PricePlanModel({
    this.id,
    this.name,
    this.nameAr,
    this.nameEn,
    this.data,
    this.description,
    this.descriptionAr,
    this.descriptionEn,
  });

  factory PricePlanModel.fromJson(Map<String?, dynamic> json) {
    return PricePlanModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      nameAr: json['name_ar'] as String?,
      nameEn: json['name_en'] as String?,
      data: json['data'] != null
          ? (json['data'] as List)
              .map((e) => PricePlanDataItem.fromJson(e))
              .toList()
          : null,
      description: json['description'] as String?,
      descriptionAr: json['description_ar'] as String?,
      descriptionEn: json['description_en'] as String?,
    );
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['name_ar'] = nameAr;
    data['name_en'] = nameEn;
    if (this.data != null) {
      data['data'] = this.data!.map((e) => e.toJson()).toList();
    }
    data['description'] = description;
    data['description_ar'] = descriptionAr;
    data['description_en'] = descriptionEn;
    return data;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        nameAr,
        nameEn,
        data,
        description,
        descriptionAr,
        descriptionEn
      ];
}

class DeveloperModel {
  int? id;
  String? name;
  String? nameAr;
  String? nameEn;
  String? description;
  String? descriptionAr;
  String? descriptionEn;
  String? developerLogo;

  DeveloperModel({
    this.id,
    this.name,
    this.nameAr,
    this.nameEn,
    this.description,
    this.descriptionAr,
    this.descriptionEn,
    this.developerLogo,
  });

  DeveloperModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nameAr = json['name_ar'];
    nameEn = json['name_en'];
    description = json['description'];
    descriptionAr = json['description_ar'];
    descriptionEn = json['description_en'];
    developerLogo = json['developer_logo'];
  }

  String get displayName {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? nameEn ?? name ?? ''
        : nameAr ?? name ?? '';
  }

  String get displayDescription {
    return LanguageController().getCurrentLanguageSync() == 'en'
        ? descriptionEn ?? description ?? ''
        : descriptionAr ?? description ?? '';
  }
}
