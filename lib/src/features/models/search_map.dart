import 'dart:convert';
import 'dart:developer';

import 'package:page/src/core/extensions/extensions.dart';

class SearchMapModel {
  int? id;

  // double distance;
  double? lat;
  double? lng;

  // String? type;
  String? name;
  String? nameAr;
  int? maincategorytype;
  num? price;
  int? typeId;
  List<int> rooms = [];

  SearchMapModel({
    this.id,
  });

  SearchMapModel.fromJson(Map<String?, dynamic> json) {
    log('adfasdasfasfasfasfsf ${json}');
    //"[2]"
    final decodedRooms = json['rooms'] != null ? jsonDecode(json['rooms']) : [];
    id = json['id'];
    name = json['name'];
    nameAr = json['name_ar'];
    price = json['start_price'] ?? json['price'] ?? 0;
    lat = json['latitude'].toString().toDouble();
    lng = json['longitude'].toString().toDouble();
    rooms = decodedRooms != null
        ? List<int>.from(
            (decodedRooms as List).map((x) => int.parse(x?.toString() ?? '0')))
        : [];
    typeId = json['type'] != null ? json['type']['id'] : null;
    // type = json['type'];
    maincategorytype = json['category'] != null ? json['category']['id'] : '';
  }
}
