import 'dart:developer';

import 'package:page/src/core/response/main_category_response.dart';
import 'package:page/src/core/response/main_response.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/agents.dart';
import 'package:page/src/features/models/project_models.dart';

class VideoModel {
  int? id;
  int? rmsPropertyId;
  int? rmsCategoryId;
  int? agentId;
  String? name;
  String? description;
  num? startprice;
  num? endprice;
  num? price;
  String? images;
  List<String> projectImages = [];
  String? video;
  String? rooms;
  int? size;
  String? type;
  String? roomnumber;

  // var label;
  String? website;
  String? whatsapp;
  String? instagram;
  String? locationName;
  MainCategoryModel? category;
  String? photo;
  String? label;
  MainModel? location;
  int? numberofstarts;
  double? latitude;
  double? longitude;
  int? isFav;
  AgentModel? agent;

  // int? parentId
  String? greviewlink;
  String? greviewName;
  String? phone;
  String? facebook;
  String? createdAt;
  String? updatedAt;
  int? rating;
  num? privateDriverPrice;
  String? brand;
  String? year;
  String? feature;
  List<String>? featureList;

  int? startSize;
  int? endSize;

  // Project-specific fields
  List<ProjectPlanModel>? projectPlans;
  List<FloorPlanModel>? floorPlans;
  ReraPermitModel? reraPermit;
  PropertyStatusModel? propertyStatus;
  String? paymentMethod;
  DeveloperModel? developer;
  PricePlanModel? pricePlan;
  int? typeId;
  int? locationId;

  VideoModel({
    this.id,
    this.rmsPropertyId,
    this.rmsCategoryId,
    this.name,
    this.description,
    this.startprice,
    this.endprice,
    this.price,
    this.images,
    this.video,
    this.typeId,
    // this.videoAr,
    this.rooms,
    this.size,
    this.type,
    this.website,
    this.instagram,
    this.locationName,
    this.category,
    this.photo,
    this.label,
    this.location,
    this.numberofstarts,
    this.latitude,
    this.longitude,
    this.whatsapp,
    this.isFav,
    this.agent,
    this.greviewlink,
    this.greviewName,
    this.phone,
    this.facebook,
    this.createdAt,
    this.updatedAt,
    this.rating,
    this.privateDriverPrice,
    this.brand,
    this.year,
    this.feature,
    this.roomnumber,
    this.startSize,
    this.featureList,
    this.endSize,
    this.projectPlans,
    this.floorPlans,
    this.reraPermit,
    this.propertyStatus,
    this.paymentMethod,
    this.developer,
    this.projectImages = const [],
    this.pricePlan,
  });

  VideoModel.fromJson(Map<String, dynamic> json) {
    category = json['category'] != null
        ? MainCategoryModel.fromJson(json['category'])
        : null;

    rmsPropertyId = json['rms_property_id'];
    rmsCategoryId = json['rms_category_id'];

    LanguageController().getcuurentlanguage().then((value) {
      final isEng = value == 'en';

      brand = json['brand_car'] != null
          ? isEng
              ? json['brand_car']['name']['en']
              : json['brand_car']['name']['ar']
          : '';

      type = json['type'] == null
          ? ''
          : json['type']['name'].runtimeType == String
              ? json['type']['name']
              : (isEng
                  ? json['type']['name']['en']
                  : json['type']['name']['ar']);
      typeId = json['type'] != null ? json['type']['id'] : null;
      locationId = json['location'] != null ? json['location']['id'] : null;
      feature = json['features'] != null && json['features'].length > 0
          ? isEng
              ? json['features'][0]['name']['en']
              : json['features'][0]['name']['ar']
          : '';

      featureList = json['features'] != null && json['features'].length > 0
          ? List<String>.from(json['features']
              .map((e) => isEng ? e['name']['en'] : e['name']['ar']))
          : [];

      // if (category?.id == AppConstants.propertiesId) {
      //   if (isEng) {
      //     video = json['video'];
      //   } else {
      //     video = json['video_ar'];
      //   }
      // } else {
      //   video = json['video'];
      // }
    });

    video = json['video'];
    whatsapp = json['whatsapp'];
    // videoAr = json['video_ar'];

    id = json['id'];
    roomnumber = json['rooms'];
    name = json['name'];
    description = json['description']?.toString() ?? '';
    rooms = json['rooms'];
    size = json['size'];

    year = json['year_car'] != null ? json['year_car']['year'] : '';

    price = json['price'];

    startSize = json['start_size'];
    endSize = json['end_size'];

    privateDriverPrice = num.tryParse(json['private_driver_price'].toString());

    agent = json['agent'] != null ? AgentModel.fromJson(json['agent']) : null;

    isFav = json['is_favorite'] == true ? 1 : 0;

    images = json['images'] != null && json['images'].isNotEmpty
        ? json['images'][0]['url']
        : '';

    projectImages = json['images'] != null
        ? List<String>.from(json['images'].map((x) => x['url']))
        : [];

    startprice = json['startprice'] == 0 ? json['price'] : json['startprice'];
    endprice = json['endprice'];
    // type = json['type']?.toString() ;

    // feature = json['feature'] == null
    //     ? ''
    //     : isEng
    //         ? json['feature']['name']['en']
    //         : json['feature']['name']['ar'];

    locationName = json['location'] != null ? json['location']['name'] : '';

    year = json['year_car'] != null ? json['year_car']['year'] : '';
    website = json['website'];
    instagram = json['instagram'];
    agentId = json['agent'] != null ? json['agent']['id'] : null;

    //////// ! ====================

    label = category?.name;

    name = json['name'];

    year = json['year_car'] != null ? json['year_car']['year'] : '';
    photo = json['images'] != null && json['images'].isNotEmpty
        ? json['images'][0]['url']
        : '';

    location =
        json['location'] == null ? null : MainModel.fromJson(json['location']);
    // numberofstarts = json['numberofstarts'];
    latitude = double.tryParse(json['latitude'].toString());
    longitude = double.tryParse(json['longitude'].toString());

    greviewlink = json['review_link'];
    greviewName = json['review_name'];
    phone = json['phone'];
    facebook = json['website'];
    instagram = json['instagram'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    rating = json['rating'];

    // Parse project-specific fields
    projectPlans = json['project_plans'] != null
        ? List<ProjectPlanModel>.from(
            json['project_plans'].map((x) => ProjectPlanModel.fromJson(x)))
        : [];

    floorPlans = json['floor_plans'] != null
        ? List<FloorPlanModel>.from(
            json['floor_plans'].map((x) => FloorPlanModel.fromJson(x)))
        : [];

    // RERA permit is single, so take first value from array and make it nullable
    reraPermit = json['rera_permits'] != null && json['rera_permits'].isNotEmpty
        ? ReraPermitModel.fromJson(json['rera_permits'][0])
        : null;

    propertyStatus = json['property_status'] != null
        ? PropertyStatusModel.fromJson(json['property_status'])
        : null;

    paymentMethod = json['payment_method'];

    developer = json['developer'] != null
        ? DeveloperModel.fromJson(json['developer'])
        : null;

    pricePlan = json['price_plan'] != null
        ? PricePlanModel.fromJson(json['price_plan'])
        : null;
  }
}

class CategoryDetails {
  int? id;
  String? name;
  String? video;
  String? description;
  double? price;
  double? endprice;
  int? isfav;

  CategoryDetails({
    this.id,
  });

  CategoryDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    video = json['video'];
    description = json['description'];
    price = json['price'];
    endprice = json['endprice'];
    isfav = json['isfav'];
  }
}

class Categoryimages {
  String? name;

  Categoryimages({
    this.name,
  });

  Categoryimages.fromJson(Map<String, dynamic> json) {
    name = json['image'];
  }
}

class Categoryreels {
  String? title;
  String? video;

  Categoryreels({
    this.title,
  });

  Categoryreels.fromJson(Map<String, dynamic> json) {
    title = json['name'];
    video = json['video'];
  }
}

// class CategoryFavorite extends VideoDetailsModel {
//   int? id;
//   int? agentId;
//   String? name;
//   int? favouriteid;
//   var startprice;
//   var price;
//   var endprice;
//   String? image;
//   String? video;
//   String? type;
//   int? isfavourite;
//   var year;
//
//   CategoryFavorite({
//     this.id,
//   });
//
//   CategoryFavorite.fromJson(Map<String, dynamic> json) {
//     id = json['video_id']['id'];
//     name = json['video_id']['name'];
//
//     image = json['video_id']['images'][0]['url'];
//     startprice = json['video_id']['startprice'];
//     endprice = json['video_id']['endprice'];
//     price = json['video_id']['price'];
//     favouriteid = json['video_id']['favourite_id'];
//     type = json['video_id']['category'] != null
//         ? json['video_id']['category']['id'].toString()
//         : null;
//     agentId = json['video_id']['agent'] != null
//         ? json['video_id']['agent']['id']
//         : null;
//     video = json['video_id']['video'];
//     year = json['video_id']['year'];
//     isfavourite = json['video_id']['is_favorite'] == true ? 1 : 0;
//   }
// }
