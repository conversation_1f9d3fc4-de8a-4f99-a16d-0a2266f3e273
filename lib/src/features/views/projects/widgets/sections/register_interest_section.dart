import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/response/profile_response.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/video_model.dart';

import '../../../../controllers/language_controller.dart';
import '../../../../models/project_models.dart';
import 'location_section.dart';

bool isLoadInitData = false;

class RegisterInterestSection extends StatefulWidget {
  final VideoModel project;
  final ProfileResponse? profileData;
  final bool isProfileDataLoaded;

  const RegisterInterestSection({
    super.key,
    required this.project,
    this.profileData,
    this.isProfileDataLoaded = false,
  });

  @override
  State<RegisterInterestSection> createState() =>
      _RegisterInterestSectionState();
}

class _RegisterInterestSectionState extends State<RegisterInterestSection> {
  final CurrencyController currencyController = CurrencyController();
  final AuthController authController = AuthController();
  final TextEditingController messageController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool isLoading = false;
  ProjectPlanModel? selectedProjectPlan;

  @override
  void initState() {
    super.initState();

    currencyController.getcuurentcurrency(context).then((value) {
      setState(() {});
    });

    authController.isloggedin();

    // Set default selected plan to first one if available
    if (widget.project.projectPlans?.isNotEmpty == true) {
      selectedProjectPlan = widget.project.projectPlans!.first;
    }

    // Populate user data if profile data is already available
    if (widget.profileData != null) {
      _populateUserData(widget.profileData);
    }
  }

  @override
  void didUpdateWidget(RegisterInterestSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update user data if profile data has changed
    if (widget.profileData != oldWidget.profileData &&
        widget.profileData != null) {
      _populateUserData(widget.profileData);
    }
  }

  String _getSelectedProjectPrice() {
    if (selectedProjectPlan != null) {
      final bedroom = selectedProjectPlan!.bedroomsEn ??
          selectedProjectPlan!.bedroomsAr ??
          '';

      final price = selectedProjectPlan!.priceFrom ?? 0;
      return '$bedroom, ${AppLocalizations.of(context).translate('Starting')} ${sortPrice(price)} ${currencyController.currency}';
    }
    return '';
  }

  void _populateUserData(ProfileResponse? profileData) {
    if (profileData?.results != null && authController.isLogged) {
      nameController.text = profileData!.results['fullname'] ?? '';
      phoneController.text = profileData.results['phone'] ?? '';
      emailController.text = profileData.results['email'] ?? '';
    }
  }

  // Method to show edit dialog for a specific field
  void _showEditDialog(String fieldName, TextEditingController controller,
      {bool isMultiline = false}) {
    if (authController.isLogged) {
      return; // Don't show dialog if user is logged in
    }

    final tempController = TextEditingController(text: controller.text);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          AppLocalizations.of(context).translate('Edit $fieldName'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: tempController,
              autofocus: true,
              minLines: isMultiline ? 3 : 1,
              maxLines: isMultiline ? 5 : 1,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).translate(fieldName),
                labelStyle: TextStyle(color: primaryColor),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: primaryColor, width: 2),
                ),
              ),
              keyboardType: fieldName == 'Phone'
                  ? TextInputType.phone
                  : fieldName == 'Email'
                      ? TextInputType.emailAddress
                      : isMultiline
                          ? TextInputType.multiline
                          : TextInputType.text,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              AppLocalizations.of(context).translate('Cancel'),
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              if (mounted) {
                setState(() {
                  controller.text = tempController.text;
                });
              }
              Navigator.pop(context);
            },
            child: Text(
              AppLocalizations.of(context).translate('Save'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  // Method to build a disabled field with edit functionality
  Widget _buildEditableField({
    required String labelKey,
    required TextEditingController controller,
    required String fieldName,
    String? Function(String?)? validator,
  }) {
    return GestureDetector(
      onTap: () => _showEditDialog(fieldName, controller),
      child: TextFormField(
        controller: controller,
        enabled: false,
        // !authController.isLogged,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).translate(labelKey),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          suffixIcon: !authController.isLogged
              ? Icon(Icons.edit, color: primaryColor, size: 20)
              : null,
          filled: true,
          fillColor:
              authController.isLogged ? Colors.grey[100] : Colors.grey[50],
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: authController.isLogged
                  ? Colors.grey[300]!
                  : Colors.grey[400]!,
            ),
          ),
        ),
        validator: validator,
      ),
    );
  }

  // Method to build a disabled message field with edit functionality
  Widget _buildEditableMessageField() {
    return GestureDetector(
      onTap: () =>
          _showEditDialog('Message', messageController, isMultiline: true),
      child: TextFormField(
        controller: messageController,
        enabled: false, // Always disabled
        minLines: 3,
        maxLines: 3,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).translate('Message'),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          suffixIcon: Icon(Icons.edit, color: primaryColor, size: 20),
          filled: true,
          fillColor: Colors.grey[50],
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[400]!),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Show loading while data is being fetched
    if (!widget.isProfileDataLoaded && authController.isLogged) {
      return buildLoadingWidget();
    }

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Name Field
          _buildEditableField(
            labelKey: 'Name',
            controller: nameController,
            fieldName: 'Name',
            validator: (value) {
              if (!authController.isLogged &&
                  (value == null || value.isEmpty)) {
                return AppLocalizations.of(context)
                    .translate('Please enter your name');
              }
              return null;
            },
          ),
          const SizedBox(height: 12),

          // Phone and Email Row
          Row(
            children: [
              Expanded(
                child: _buildEditableField(
                  labelKey: 'Phone',
                  controller: phoneController,
                  fieldName: 'Phone',
                  validator: (value) {
                    if (!authController.isLogged &&
                        (value == null || value.isEmpty)) {
                      return AppLocalizations.of(context)
                          .translate('Please enter your phone');
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEditableField(
                  labelKey: 'Email',
                  controller: emailController,
                  fieldName: 'Email',
                  validator: (value) {
                    if (!authController.isLogged &&
                        (value == null || value.isEmpty)) {
                      return AppLocalizations.of(context)
                          .translate('Please enter your email');
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Project Name Field (no price field as requested)
          TextFormField(
            enabled: false,
            initialValue: widget.project.name ?? '',
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).translate('Project Name'),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 12),

          // Project Plan Dropdown
          if (widget.project.projectPlans?.isNotEmpty == true) ...[
            DropdownButtonFormField<ProjectPlanModel>(
              value: selectedProjectPlan,
              isExpanded: true,
              borderRadius: BorderRadius.circular(12),
              dropdownColor: Colors.white,
              decoration: InputDecoration(
                hoverColor: Colors.transparent,
                labelText:
                    AppLocalizations.of(context).translate('Select Unit'),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              items: widget.project.projectPlans!.map((plan) {
                final bedroom = isEnglish(context)
                    ? (plan.bedroomsEn ?? plan.bedroomsAr ?? '')
                    : (plan.bedroomsAr ?? plan.bedroomsEn ?? '');
                final price = plan.priceFrom ?? 0;
                final displayText =
                    '$bedroom, ${AppLocalizations.of(context).translate('Starting')} ${sortPrice(price)} ${currencyController.currency}';

                return DropdownMenuItem<ProjectPlanModel>(
                  value: plan,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      displayText,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (ProjectPlanModel? newValue) {
                setState(() {
                  selectedProjectPlan = newValue;
                });
              },
              validator: (value) {
                if (value == null) {
                  return AppLocalizations.of(context)
                      .translate('Please select a plan');
                }
                return null;
              },
            ),
            const SizedBox(height: 12),
          ],

          // Message Field
          _buildEditableMessageField(),
          const SizedBox(height: 24),

          // Send Button
          !isLoading
              ? SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    onPressed: () async {
                      // Validate form
                      if (!_formKey.currentState!.validate()) {
                        return;
                      }

                      // Check login for non-logged users
                      if (!authController.isLogged) {
                        // For non-logged users, all fields should be filled
                        if (nameController.text.isEmpty ||
                                phoneController.text.isEmpty
                            // ||
                            // emailController.text.isEmpty
                            ) {
                          snackbar(AppLocalizations.of(context)
                              .translate('Please fill all required fields'));
                          return;
                        }
                      }

                      setState(() {
                        isLoading = true;
                      });

                      try {
                        await Api.sendMessage(
                          propertyId: widget.project.id ?? 0,
                          propertyName: widget.project.name ?? '',
                          propertyPrice: _getSelectedProjectPrice(),
                          message: messageController.text,
                          userName: authController.isLogged
                              ? widget.profileData?.results['fullname'] ?? ''
                              : nameController.text,
                          userPhone: authController.isLogged
                              ? widget.profileData?.results['phone'] ?? ''
                              : phoneController.text,
                          userEmail: authController.isLogged
                              ? widget.profileData?.results['email'] ?? ''
                              : emailController.text,
                          userId: authController.isLogged
                              ? widget.profileData?.results['id']?.toString()
                              : null,
                        );

                        setState(() {
                          isLoading = false;
                        });

                        messageController.clear();
                        snackbar2(AppLocalizations.of(context)
                            .translate('Sent Successfully'));
                      } catch (e) {
                        log('Cannot send message: $e');
                        snackbar(AppLocalizations.of(context).translate(
                            'Something went wrong, please try again later'));
                        setState(() {
                          isLoading = false;
                        });
                      }
                    },
                    child: Text(
                      AppLocalizations.of(context).translate('Send'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ),
                )
              : Center(
                  child: Lottie.asset(
                    'assets/59218-progress-indicator.json',
                    height: 50,
                    width: 50,
                  ),
                ),
        ],
      ),
    );
  }
}
