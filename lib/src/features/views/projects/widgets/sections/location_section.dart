import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/map/show_on_map/projects_map_page.dart';

bool mapLoaded = false;
ValueNotifier<Widget?> cachedMap = ValueNotifier(null);

class LocationSection extends StatefulWidget {
  final VideoModel project;

  const LocationSection({
    super.key,
    required this.project,
  });

  @override
  State<LocationSection> createState() => _LocationSectionState();
}

class _LocationSectionState extends State<LocationSection>
    with AutomaticKeepAliveClientMixin {
  late final Set<Marker> _markers;
  MapType _currentMapType = MapType.normal;

  @override
  void initState() {
    super.initState();
    if (widget.project.latitude != null &&
        widget.project.longitude != null &&
        !mapLoaded) {
      _markers = _setupMarker();
      cachedMap?.value = GoogleMap(
        initialCameraPosition: CameraPosition(
          target: LatLng(widget.project.latitude!, widget.project.longitude!),
          zoom: 15,
        ),
        markers: _markers,
        mapType: _currentMapType,
        mapToolbarEnabled: false,
        myLocationButtonEnabled: false,
        gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
          Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
        },
      );
      mapLoaded = true;
    }
  }

  @override
  bool get wantKeepAlive => true;

  Set<Marker> _setupMarker() {
    log('widget.project.latitude ${widget.project.latitude}');
    if (widget.project.latitude != null && widget.project.longitude != null) {
      final marker = Marker(
        markerId: const MarkerId('project_location'),
        position: LatLng(widget.project.latitude!, widget.project.longitude!),
        infoWindow: InfoWindow(
          title: widget.project.name ?? 'Project Location',
          snippet: widget.project.locationName,
        ),
      );
      return {marker};
    }
    return {};
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // required when using AutomaticKeepAliveClientMixin
    if (widget.project.latitude == null || widget.project.longitude == null) {
      return _buildNoLocationWidget(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location info
        if (widget.project.locationName?.isNotEmpty == true) ...[
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: Colors.green[700],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context).translate('Location'),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.green[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        widget.project.locationName!,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[800],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
        ],

        // Map preview
        GestureDetector(
          onPanUpdate:
              (_) {}, // Prevent parent scroll when interacting with map
          child: Container(
            height: 340,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Use cached map to avoid rebuilds when keyboard opens
                  // if (cachedMap != null) cachedMap!.value,
                  ValueListenableBuilder<Widget?>(
                    valueListenable: cachedMap,
                    builder: (context, value, child) {
                      if (value == null) {
                        return const SizedBox();
                      }

                      return value;
                    },
                  ),
                  // // Map type toggle button
                  Positioned(
                    top: 10,
                    right: isEnglish(context) ? 10 : null,
                    left: isEnglish(context) ? null : 10,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            setState(() {
                              _currentMapType =
                                  _currentMapType == MapType.normal
                                      ? MapType.satellite
                                      : MapType.normal;
                              cachedMap.value = GoogleMap(
                                initialCameraPosition: CameraPosition(
                                  target: LatLng(widget.project.latitude!,
                                      widget.project.longitude!),
                                  zoom: 15,
                                ),
                                markers: _markers,
                                mapType: _currentMapType,
                                mapToolbarEnabled: false,
                                myLocationButtonEnabled: false,
                                gestureRecognizers: <Factory<
                                    OneSequenceGestureRecognizer>>{
                                  Factory<OneSequenceGestureRecognizer>(
                                      () => EagerGestureRecognizer()),
                                },
                              );
                            });
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(8),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _currentMapType == MapType.normal
                                      ? Icons.satellite_alt
                                      : Icons.map,
                                  size: 16,
                                  color: Colors.grey[700],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _currentMapType == MapType.normal
                                      ? AppLocalizations.of(context)
                                          .translate('Satellite')
                                      : AppLocalizations.of(context)
                                          .translate('Normal'),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // const SizedBox(height: 16),
        //
        // // Action buttons
        // Row(
        //   children: [
        //     Expanded(
        //       child: ElevatedButton.icon(
        //         onPressed: () => _openFullMap(context),
        //         icon: const Icon(Icons.map, size: 18),
        //         label: Text(
        //           AppLocalizations.of(context).translate('View on Map'),
        //           style: const TextStyle(fontSize: 14),
        //         ),
        //         style: ElevatedButton.styleFrom(
        //           backgroundColor: const Color(0xFF27b4a8),
        //           foregroundColor: Colors.white,
        //           padding: const EdgeInsets.symmetric(vertical: 12),
        //           shape: RoundedRectangleBorder(
        //             borderRadius: BorderRadius.circular(8),
        //           ),
        //         ),
        //       ),
        //     ),
        //     const SizedBox(width: 12),
        //     Expanded(
        //       child: OutlinedButton.icon(
        //         onPressed: () => _openDirections(context),
        //         icon: const Icon(Icons.directions, size: 18),
        //         label: Text(
        //           AppLocalizations.of(context).translate('Get Directions'),
        //           style: const TextStyle(fontSize: 14),
        //         ),
        //         style: OutlinedButton.styleFrom(
        //           foregroundColor: const Color(0xFF27b4a8),
        //           side: const BorderSide(color: Color(0xFF27b4a8)),
        //           padding: const EdgeInsets.symmetric(vertical: 12),
        //           shape: RoundedRectangleBorder(
        //             borderRadius: BorderRadius.circular(8),
        //           ),
        //         ),
        //       ),
        //     ),
        //   ],
        // ),
      ],
    );
  }

  Widget _buildNoLocationWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.location_off,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context).translate('Location Not Available'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context)
                  .translate('Location information will be added soon'),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// import 'dart:developer';
//
// import 'package:flutter/foundation.dart';
// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:keyboard_detection/keyboard_detection.dart';
// import 'package:page/src/core/localization/app_localizations.dart';
// import 'package:page/src/features/controllers/language_controller.dart';
// import 'package:page/src/features/models/video_model.dart';
// import 'package:page/src/features/views/map/show_on_map/projects_map_page.dart';
//
// bool mapLoaded = false;
//
// class LocationSection extends StatefulWidget {
//   final VideoModel project;
//
//   const LocationSection({
//     super.key,
//     required this.project,
//   });
//
//   @override
//   State<LocationSection> createState() => _LocationSectionState();
// }
//
// class _LocationSectionState extends State<LocationSection>
//     with AutomaticKeepAliveClientMixin {
//   late final Set<Marker> _markers;
//   MapType _currentMapType = MapType.normal;
//   late KeyboardDetectionController keyboardDetectionController;
//
//   Widget? _cachedMap;
//   KeyboardState _keyboardState = KeyboardState.unknown;
//
//   @override
//   void initState() {
//     super.initState();
//
//     _initializeMap();
//   }
//
//   void _initializeMap() {
//     if (widget.project.latitude != null &&
//         widget.project.longitude != null &&
//         !mapLoaded) {
//       _markers = _setupMarker();
//       _cachedMap = GoogleMap(
//         initialCameraPosition: CameraPosition(
//           target: LatLng(widget.project.latitude!, widget.project.longitude!),
//           zoom: 15,
//         ),
//         markers: _markers,
//         mapType: _currentMapType,
//         mapToolbarEnabled: false,
//         myLocationButtonEnabled: false,
//         gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
//           Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
//         },
//       );
//       mapLoaded = true;
//     }
//   }
//
//   @override
//   bool get wantKeepAlive => true;
//
//   Set<Marker> _setupMarker() {
//     log('widget.project.latitude ${widget.project.latitude}');
//     if (widget.project.latitude != null && widget.project.longitude != null) {
//       final marker = Marker(
//         markerId: const MarkerId('project_location'),
//         position: LatLng(widget.project.latitude!, widget.project.longitude!),
//         infoWindow: InfoWindow(
//           title: widget.project.name ?? 'Project Location',
//           snippet: widget.project.locationName,
//         ),
//       );
//       return {marker};
//     }
//     return {};
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     super.build(context); // required when using AutomaticKeepAliveClientMixin
//     if (widget.project.latitude == null || widget.project.longitude == null) {
//       return _buildNoLocationWidget(context);
//     }
//     // Initialize keyboard detection controller
//     keyboardDetectionController = KeyboardDetectionController(
//       onChanged: (value) {
//         log('Keyboard state changed: $value');
//
//         // If keyboard was visible and now it's hidden, reset mapLoaded and rebuild
//         if (_keyboardState == KeyboardState.visible &&
//             value == KeyboardState.hidden) {
//           WidgetsBinding.instance.addPostFrameCallback((_) {
//             if (mounted) {
//               setState(() {
//                 log('asfasfasf');
//                 mapLoaded = false; // Reset to allow rebuild
//                 _initializeMap(); // Rebuild the map
//               });
//             }
//           });
//         }
//
//         _keyboardState = value;
//       },
//     );
//
//     return KeyboardDetection(
//       controller: keyboardDetectionController,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Location info
//           if (widget.project.locationName?.isNotEmpty == true) ...[
//             Container(
//               padding: const EdgeInsets.all(8),
//               decoration: BoxDecoration(
//                 color: Colors.green[50],
//                 borderRadius: BorderRadius.circular(12),
//                 border: Border.all(color: Colors.green[200]!),
//               ),
//               child: Row(
//                 children: [
//                   Icon(
//                     Icons.location_on,
//                     color: Colors.green[700],
//                     size: 24,
//                   ),
//                   const SizedBox(width: 12),
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           AppLocalizations.of(context).translate('Location'),
//                           style: TextStyle(
//                             fontSize: 14,
//                             color: Colors.green[800],
//                             fontWeight: FontWeight.w500,
//                           ),
//                         ),
//                         Text(
//                           widget.project.locationName!,
//                           style: TextStyle(
//                             fontSize: 16,
//                             fontWeight: FontWeight.bold,
//                             color: Colors.green[800],
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             const SizedBox(height: 12),
//           ],
//
//           // Map preview
//           GestureDetector(
//             onPanUpdate:
//                 (_) {}, // Prevent parent scroll when interacting with map
//             child: Container(
//               height: 340,
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(12),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.black.withOpacity(0.1),
//                     blurRadius: 8,
//                     offset: const Offset(0, 2),
//                   ),
//                 ],
//               ),
//               child: ClipRRect(
//                 borderRadius: BorderRadius.circular(12),
//                 child: Stack(
//                   children: [
//                     // Use cached map to avoid rebuilds when keyboard opens
//                     if (_cachedMap != null) _cachedMap!,
//                     // // Map type toggle button
//                     Positioned(
//                       top: 10,
//                       right: isEnglish(context) ? 10 : null,
//                       left: isEnglish(context) ? null : 10,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(8),
//                           boxShadow: [
//                             BoxShadow(
//                               color: Colors.black.withOpacity(0.2),
//                               blurRadius: 4,
//                               offset: const Offset(0, 2),
//                             ),
//                           ],
//                         ),
//                         child: Material(
//                           color: Colors.transparent,
//                           child: InkWell(
//                             borderRadius: BorderRadius.circular(8),
//                             onTap: () {
//                               setState(() {
//                                 _currentMapType =
//                                     _currentMapType == MapType.normal
//                                         ? MapType.satellite
//                                         : MapType.normal;
//                               });
//                               // _cachedMap = GoogleMap(
//                               //   initialCameraPosition: CameraPosition(
//                               //     target: LatLng(widget.project.latitude!, widget.project.longitude!),
//                               //     zoom: 15,
//                               //   ),
//                               //   markers: _markers,
//                               //   mapType: _currentMapType,
//                               //   mapToolbarEnabled: false,
//                               //   myLocationButtonEnabled: false,
//                               //   gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
//                               //     Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
//                               //   },
//                               // );
//                               // _cachedMap = GoogleMap(
//                               //   initialCameraPosition: CameraPosition(
//                               //     target: LatLng(widget.project.latitude!,
//                               //         widget.project.longitude!),
//                               //     zoom: 15,
//                               //   ),
//                               //   markers: _markers,
//                               //   mapType: _currentMapType,
//                               //   mapToolbarEnabled: false,
//                               //   myLocationButtonEnabled: false,
//                               //   gestureRecognizers: <Factory<
//                               //       OneSequenceGestureRecognizer>>{
//                               //     Factory<OneSequenceGestureRecognizer>(
//                               //         () => EagerGestureRecognizer()),
//                               //   },
//                               // );
//                               // });
//                             },
//                             child: Padding(
//                               padding: const EdgeInsets.all(8),
//                               child: Row(
//                                 mainAxisSize: MainAxisSize.min,
//                                 children: [
//                                   Icon(
//                                     _currentMapType == MapType.normal
//                                         ? Icons.satellite_alt
//                                         : Icons.map,
//                                     size: 16,
//                                     color: Colors.grey[700],
//                                   ),
//                                   const SizedBox(width: 4),
//                                   Text(
//                                     _currentMapType == MapType.normal
//                                         ? AppLocalizations.of(context)
//                                             .translate('Satellite')
//                                         : AppLocalizations.of(context)
//                                             .translate('Normal'),
//                                     style: TextStyle(
//                                       fontSize: 12,
//                                       color: Colors.grey[700],
//                                       fontWeight: FontWeight.w500,
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//
//           // const SizedBox(height: 16),
//           //
//           // // Action buttons
//           // Row(
//           //   children: [
//           //     Expanded(
//           //       child: ElevatedButton.icon(
//           //         onPressed: () => _openFullMap(context),
//           //         icon: const Icon(Icons.map, size: 18),
//           //         label: Text(
//           //           AppLocalizations.of(context).translate('View on Map'),
//           //           style: const TextStyle(fontSize: 14),
//           //         ),
//           //         style: ElevatedButton.styleFrom(
//           //           backgroundColor: const Color(0xFF27b4a8),
//           //           foregroundColor: Colors.white,
//           //           padding: const EdgeInsets.symmetric(vertical: 12),
//           //           shape: RoundedRectangleBorder(
//           //             borderRadius: BorderRadius.circular(8),
//           //           ),
//           //         ),
//           //       ),
//           //     ),
//           //     const SizedBox(width: 12),
//           //     Expanded(
//           //       child: OutlinedButton.icon(
//           //         onPressed: () => _openDirections(context),
//           //         icon: const Icon(Icons.directions, size: 18),
//           //         label: Text(
//           //           AppLocalizations.of(context).translate('Get Directions'),
//           //           style: const TextStyle(fontSize: 14),
//           //         ),
//           //         style: OutlinedButton.styleFrom(
//           //           foregroundColor: const Color(0xFF27b4a8),
//           //           side: const BorderSide(color: Color(0xFF27b4a8)),
//           //           padding: const EdgeInsets.symmetric(vertical: 12),
//           //           shape: RoundedRectangleBorder(
//           //             borderRadius: BorderRadius.circular(8),
//           //           ),
//           //         ),
//           //       ),
//           //     ),
//           //   ],
//           // ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildNoLocationWidget(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.all(24),
//       decoration: BoxDecoration(
//         color: Colors.grey[100],
//         borderRadius: BorderRadius.circular(12),
//         border: Border.all(color: Colors.grey[300]!),
//       ),
//       child: Center(
//         child: Column(
//           children: [
//             Icon(
//               Icons.location_off,
//               size: 48,
//               color: Colors.grey[400],
//             ),
//             const SizedBox(height: 12),
//             Text(
//               AppLocalizations.of(context).translate('Location Not Available'),
//               style: TextStyle(
//                 color: Colors.grey[600],
//                 fontSize: 16,
//                 fontWeight: FontWeight.w500,
//               ),
//             ),
//             const SizedBox(height: 4),
//             Text(
//               AppLocalizations.of(context)
//                   .translate('Location information will be added soon'),
//               style: TextStyle(
//                 color: Colors.grey[500],
//                 fontSize: 14,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
