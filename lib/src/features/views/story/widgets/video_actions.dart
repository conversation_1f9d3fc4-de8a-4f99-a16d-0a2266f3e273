import 'package:flutter/material.dart';
// import 'package:flutter_ffmpeg/flutter_ffmpeg.dart';

import 'package:flutter_svg/svg.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/models/reels.dart';
import 'package:page/src/features/views/send_holiday_request/send_holiday_home_request.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';

import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../core/utils/dynamic_links.dart';
import '../../../controllers/language_controller.dart';
import '../../send_request_car/send_request_car_rental.dart';

class VideoActions extends StatelessWidget {
  final Reels story;
  final StateSetter setState;
  final VideoPlayerController? videoController;

  const VideoActions(
      {Key? key,
      required this.story,
      required this.setState,
      required this.videoController})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isEnglish(context)
        ? Positioned(bottom: 30, right: 20, child: _actions(context))
        : Positioned(bottom: 30, left: 20, child: _actions(context));
  }

  Widget _actions(BuildContext context) {
    final AuthController authController = AuthController();

    final Widget svg5 = SizedBox(
        width: 22,
        height: 25,
        child: SvgPicture.asset(
          'assets/Group 7408.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return Column(
      children: [
        story.categorytype == "car_rent" || story.categorytype == "holiday_home"
            ? Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: InkWell(
                  onTap: () {
                    Navigator.of(context).pushReplacement(MaterialPageRoute(
                        builder: (BuildContext context) => SendHolidayRequest(
                              story.id!,
                              rmsCategoryId: story.rmsCategoryId!,
                              numOfRooms: story.numOfRooms,
                              agentId: story.agentId!,
                              startPrice: story.startPrice,
                              propertyName: story.name,
                            )));
                    // story.categorytype == "car_rent"
                    //     ? authController.isLogged == true
                    //         ? Navigator.of(context).pushReplacement(
                    //             MaterialPageRoute(
                    //                 builder: (BuildContext context) =>
                    //                     SendRequestCarRental(
                    //                       story.id!,
                    //                       agentId: story.agentId!,
                    //                       startPrice: story.carPrice,
                    //                       privateDriverPrice:
                    //                           story.privateDriverPrice,
                    //                     )))
                    //         : snackbar(AppLocalizations.of(context)
                    //             .translate('Please login first'))
                    //     : story.categorytype == "holiday_home"
                    //         ?
                    // authController.isLogged == true
                    //             ?
                    // Navigator.of(context).pushReplacement(
                    //                 MaterialPageRoute(
                    //                     builder: (BuildContext context) =>
                    //                         SendHolidayRequest(
                    //                           story.id!,
                    //                           rmsCategoryId:
                    //                               story.rmsCategoryId!,
                    //                           numOfRooms: story.numOfRooms,
                    //                           agentId: story.agentId!,
                    //                           startPrice: story.startPrice,
                    //                           propertyName: story.name,
                    //                         )))
                    // : snackbar(AppLocalizations.of(context)
                    //     .translate('Please login first'))
                    // ignore: unnecessary_statements
                    // : null;
                  },
                  child: svg5,
                ),
              )
            : const SizedBox(),

        // SizedBox(
        //   height: 8,
        // ),
        // authController.islogin == true
        //     ? story.isfavourite == 1
        //         ? InkWell(
        //             onTap: () async {
        //               GeneralResponse sucessinformation =
        //                   await Api.removemainreelfavourite(story.reelid!);
        //               print(sucessinformation.code);
        //               if (sucessinformation.code == "1") {
        //                 snackbar2(AppLocalizations.of(context)
        //                     .translate('remove from favourite successfuly'));
        //                 setState(() {
        //                   story.isfavourite = 0;
        //                   // isfav = true;
        //                 });
        //               } else {
        //                 snackbar(AppLocalizations.of(context).translate(
        //                     'Something went wrong, please try again later'));
        //               }
        //             },
        //             child: const Icon(
        //               Icons.favorite,
        //               color: Colors.red,
        //             ))
        //         : InkWell(
        //             onTap: () async {
        //               GeneralResponse sucessinformation =
        //                   await Api.addreelfavourite(story.reelid!);
        //               print(sucessinformation.code);
        //               if (sucessinformation.code == "1") {
        //                 snackbar2(AppLocalizations.of(context)
        //                     .translate('add to favourite successfuly'));
        //                 setState(() {
        //                   story.isfavourite = 1;
        //                 });
        //               } else {
        //                 snackbar(AppLocalizations.of(context).translate(
        //                     'Something went wrong, please try again later'));
        //               }
        //             },
        //             child: const Icon(
        //               Icons.favorite_outline,
        //               color: Colors.white,
        //             ))
        //     : InkWell(
        //         onTap: () async {
        //           snackbar(AppLocalizations.of(context)
        //               .translate('Please login first'));
        //         },
        //         child: const Icon(
        //           Icons.favorite_outline,
        //           color: Colors.white,
        //         )),
        const SizedBox(
          height: 20,
        ),
        InkWell(
            onTap: () async {
              setState(() {
                videoController?.pause();
              });
              final String linkPathData = '?id=${story.reelid}&type=${'reels'}';
              final dynamicLink = await DynamicLinkHandler.createDynamicLink(
                linkPathData,
              );

              Share.share(dynamicLink.toString());
            },
            child: const Icon(Icons.share, color: Colors.white, size: 30))
      ],
    );
  }
}
