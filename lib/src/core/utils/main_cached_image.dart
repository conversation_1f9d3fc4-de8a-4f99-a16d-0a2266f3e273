import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class MainCachedImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit fit;
  final Widget? errorWidget;
  final double? radius;

  const MainCachedImage(
    this.imageUrl, {
    super.key,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.errorWidget,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    final loadingImage = LoadingWidget(
      height: height,
      width: width,
      radius: radius,
    );

    if (kIsWeb) {
      //   return Image(
      //     image: NetworkImage(
      //       imageUrl,
      //       webHtmlElementStrategy: WebHtmlElementStrategy.prefer,
      //     ),
      //     height: height ?? 230,
      //     width: width ?? 150,
      //     fit: BoxFit.fitWidth,
      //     errorBuilder: (context, error, stackTrace) =>
      //         errorWidget ?? loadingImage,
      //     loadingBuilder: (context, child, loadingProgress) {
      //       if (loadingProgress == null) {
      //         return child;
      //       } else {
      //         return loadingImage;
      //       }
      //     },
      //   );
      // }

      return SizedBox(
        height: height,
        width: width,
        child: Image.network(
          imageUrl,
          height: height ?? 230,
          width: width ?? 150,
          fit: kIsWeb ? BoxFit.fitWidth : BoxFit.cover,
          errorBuilder: (context, error, stackTrace) =>
              errorWidget ?? loadingImage,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) {
              return child;
            } else {
              return loadingImage;
            }
          },
        ),
      );
    }

    return SizedBox(
      height: height,
      width: width,
      child: FastCachedImage(
        fadeInDuration: const Duration(milliseconds: 400),
        url: imageUrl,
        height: height,
        width: width,
        fit: fit,
        // cacheHeight: height?.toInt(),
        // cacheWidth: width?.toInt(),
        loadingBuilder: (context, url) => loadingImage,
        errorBuilder: (context, url, error) => errorWidget ?? loadingImage,
      ),
    );
  }
}

class LoadingWidget extends StatelessWidget {
  final double? height;
  final double? width;
  final double? radius;

  const LoadingWidget({super.key, this.height, this.width, this.radius});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Shimmer(
        gradient: LinearGradient(
          colors: [
            Colors.grey[300]!,
            Colors.grey[100]!,
          ],
        ),
        child: Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: radius == null
                  ? null
                  : BorderRadius.all(Radius.circular(radius!)),
            )),
      ),
    );
  }
}
