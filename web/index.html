<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Dubai Page">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Dubai Page">
  <link rel="apple-touch-icon" href="favicon.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png">

  <title>Aqar Dubai</title>
  <link rel="manifest" href="manifest.json">

  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        name="viewport">

  <!-- Flutter initialization -->
  <script src="flutter.js" defer></script>
</head>
<body style="margin:0; background-color: transparent;">
<script>
  window.addEventListener('load', function() {
    // Use the modern Flutter web initialization
    if ('serviceWorker' in navigator) {
      // Service worker support
      _flutter.loader.load({
        serviceWorkerSettings: {
          serviceWorkerVersion: "{{flutter_service_worker_version}}",
        },
      }).then(function(engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        return appRunner.runApp();
      });
    } else {
      // Fallback for browsers without service worker support
      _flutter.loader.load().then(function(engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        return appRunner.runApp();
      });
    }
  });
</script>

<script type="text/javascript">
  window.flutterWebRenderer = "html";
</script>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAcl3LOVhYIjlWPWjuONGesjhEeDzRPmlY"></script>
</body>
</html>