name: page
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.1.37+37

environment:
  sdk: ^3.6.1


dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  printing: ^5.13.3
#    path: packages/printing_package

  progress_dialog_null_safe:
    path: packages/progress

  multi_video_player:
    path: packages/multi_video_player

  cupertino_icons: ^1.0.5
  country_picker: ^2.0.20
  pin_code_fields: ^8.0.1
  flutter_svg: ^2.0.6
  flutter_staggered_grid_view: ^0.4.0
  flutter_rating_bar: ^4.0.1
  google_maps_flutter: ^2.3.0
  datetime_picker_formfield: ^2.0.1
  dotted_line: ^3.1.0
  font_awesome_flutter:
  share_plus:
  get: ^4.6.5
  dio: ^4.0.0
  fluttertoast: ^8.2.12
  rxdart: ^0.27.7
  shared_preferences: ^2.0.18
  lottie: ^2.6.0
  whatsapp_unilink: ^2.1.0
  pull_to_refresh: ^2.0.0
  geolocator: ^9.0.2
  provider: ^6.0.5
  #  flutter_local_notifications: ^14.1.1
  #  get_storage: ^2.1.1
  url_launcher: ^6.1.11
  google_translator: ^1.0.0
  google_cloud_translation:
  flutter_swiper_plus: ^2.0.4
#  flutter_video_info: ^1.3.2
#  video_box: ^0.17.0
  permission_handler:
  flutter_typeahead: ^4.7.0
  carousel_slider: ^5.1.1
  flutter_html: ^3.0.0-beta.2
  dots_indicator: ^3.0.0
  path_provider: ^2.0.15
  intl: ^0.20.2
  country_pickers: ^3.0.1
  flutter_screenutil: ^5.8.1
  flutter_phoenix: ^1.1.1
  flutter_widget_from_html_core: ^0.16.0
#  flutter_phone_direct_caller:
#    path: packages/flutter_phone_direct_caller
#  firebase_messaging: ^14.6.2
  firebase_messaging: ^15.2.1
  firebase_core: ^3.10.1
#  firebase_core: ^2.13.1
  firebase_dynamic_links:
  equatable: ^2.0.5
  map_launcher: ^3.5.0
  fast_cached_network_image: ^1.3.3+5
  shimmer:
  google_fonts:
  open_file: ^3.5.10
  easy_image_viewer: ^1.5.1
  vertical_scrollable_tabview: ^0.1.1
  scroll_to_index: ^3.0.1
  #  video_player: ^2.7.1
  video_player: ^2.9.3
  google_api_headers: ^4.5.1
  firebase_auth:
  flutter_cache_manager: ^3.4.1
#  sort_price: ^0.0.3
#  calendar_date_picker2: ^0.5.3
  calendar_date_picker2:
    path: packages/calendar_date_picker2
  logger:
  ip_country_lookup: ^1.0.2
  connectivity_plus:
  app_tracking_transparency: ^2.0.4
  universal_platform: ^1.1.0
  widget_to_marker: ^1.0.6
  flutter_hooks:
  webview_flutter: ^4.13.0
  keyboard_detection: ^0.8.0

dependency_overrides:
  dio: ^4.0.0
  intl: ^0.20.2
  flutter_keyboard_visibility: ^6.0.0
  archive: ^3.6.1
  win32: ^5.5.4
  package_info_plus: ^8.3.0
  video_player_android: ^2.8.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints:
  flutter_launcher_icons: ^0.14.3
#  flutter_native_splash:

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/launcher_dubai_logo.png"
  adaptive_icon_background: "assets/images/launcher_dubai_logo.png"
  adaptive_icon_foreground: "assets/images/launcher_dubai_logo.png"
  remove_alpha_ios: true

#? dart run flutter_launcher_icons:main

# dart run flutter_native_splash:create
# dart run flutter_native_splash:remove

#flutter_native_splash:
#  image: assets/images/resized_splash.png
#  color: "#F8F0E4"
#    color: "#E6CB9C"
#  android: true
#  ios: true


flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/launcher_dubai_logo.png"


flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/
    - assets/icons/
    - assets/images/
    - assets/animated/
    - assets/fonts/
    - assets/fonts/cairo/
    - i18n/en.json
    - i18n/ar.json
    - shorebird.yaml


  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Medium.TTF
        - asset: assets/fonts/Roboto-Regular.TTF

    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Light.ttf
        - asset: assets/fonts/Tajawal-Medium.ttf

    - family: Cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-Regular.ttf
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
        - asset: assets/fonts/cairo/Cairo-Black.ttf
        - asset: assets/fonts/cairo/Cairo-ExtraLight.ttf
        - asset: assets/fonts/cairo/Cairo-Light.ttf
        - asset: assets/fonts/cairo/Cairo-SemiBold.ttf

#  pod cache clean --all